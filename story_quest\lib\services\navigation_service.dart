/// Navigation service for Story Quest app
/// 
/// Manages app navigation, routing, and screen transitions
/// with proper back navigation handling.
library;

import 'package:flutter/material.dart';
import '../features/splash/splash_screen.dart';
import '../features/ftue/ftue_screen.dart';
import '../features/profile_selection/child_profile_selection_screen.dart';
import '../features/homepage/homepage_screen.dart';

/// Route names for the application
class AppRoutes {
  static const String splash = '/';
  static const String ftue = '/ftue';
  static const String profileSelection = '/profile-selection';
  static const String homepage = '/homepage';
}

/// Navigation service for managing app navigation
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Gets the current context
  static BuildContext? get currentContext => navigatorKey.currentContext;

  /// Navigates to a named route
  static Future<T?> navigateTo<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamed<T>(routeName, arguments: arguments);
  }

  /// Navigates to a route and removes all previous routes
  static Future<T?> navigateAndClearStack<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// Replaces the current route
  static Future<T?> navigateAndReplace<T>(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushReplacementNamed<T>(routeName, arguments: arguments);
  }

  /// Goes back to the previous screen
  static void goBack<T>([T? result]) {
    if (navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop<T>(result);
    }
  }

  /// Checks if we can go back
  static bool canGoBack() {
    return navigatorKey.currentState?.canPop() ?? false;
  }

  /// Shows a dialog
  static Future<T?> showDialogRoute<T>(Widget dialog) {
    return showDialog<T>(
      context: currentContext!,
      builder: (context) => dialog,
    );
  }

  /// Shows a bottom sheet
  static Future<T?> showBottomSheetRoute<T>(Widget bottomSheet) {
    return showModalBottomSheet<T>(
      context: currentContext!,
      builder: (context) => bottomSheet,
    );
  }
}

/// Route generator for the application
class AppRouteGenerator {
  /// Generates routes based on route settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return _createRoute(
          SplashScreen(
            onContinue: () => NavigationService.navigateAndReplace(AppRoutes.ftue),
          ),
        );

      case AppRoutes.ftue:
        return _createRoute(
          FTUEScreen(
            onCreateAccount: () => NavigationService.navigateTo(AppRoutes.profileSelection),
            onLogin: () => NavigationService.navigateTo(AppRoutes.profileSelection),
            onGoogleSignIn: () => NavigationService.navigateTo(AppRoutes.profileSelection),
            onAppleSignIn: () => NavigationService.navigateTo(AppRoutes.profileSelection),
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.profileSelection:
        return _createRoute(
          ChildProfileSelectionScreen(
            profiles: DefaultProfiles.profiles,
            onProfileSelected: (profile) {
              NavigationService.navigateAndReplace(AppRoutes.homepage);
            },
            onAddProfile: () {
              // TODO: Implement add profile functionality
              _showAddProfileDialog();
            },
            onBack: () => NavigationService.goBack(),
          ),
        );

      case AppRoutes.homepage:
        return _createRoute(
          HomepageScreen(
            onStoryTime: () {
              // TODO: Navigate to story library
              _showComingSoonDialog('Story Library');
            },
            onMyStuff: () {
              // TODO: Navigate to my stuff
              _showComingSoonDialog('My Stuff');
            },
            onResumeStories: () {
              // TODO: Navigate to resume stories
              _showComingSoonDialog('Resume Stories');
            },
            onMyRewards: () {
              // TODO: Navigate to rewards
              _showComingSoonDialog('My Rewards');
            },
            onParentZone: () {
              // TODO: Navigate to parent zone
              _showComingSoonDialog('Parent Zone');
            },
            onTutorials: () {
              // TODO: Navigate to tutorials
              _showComingSoonDialog('Tutorials');
            },
            onThemeChanged: (theme) {
              // TODO: Implement theme switching
              print('Theme changed to: ${theme.label}');
            },
          ),
        );

      default:
        return _createRoute(
          const Scaffold(
            body: Center(
              child: Text('Route not found'),
            ),
          ),
        );
    }
  }

  /// Creates a page route with slide transition
  static PageRoute _createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.ease;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// Shows a coming soon dialog
  static void _showComingSoonDialog(String feature) {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: Text('$feature Coming Soon!'),
        content: Text('The $feature feature will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Shows add profile dialog
  static void _showAddProfileDialog() {
    NavigationService.showDialogRoute(
      AlertDialog(
        title: const Text('Add New Profile'),
        content: const Text('Profile creation will be available in a future update.'),
        actions: [
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => NavigationService.goBack(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Custom page route with fade transition
class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;

  FadePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        );
}

/// Custom page route with scale transition
class ScalePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;

  ScalePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(
                CurvedAnimation(
                  parent: animation,
                  curve: Curves.elasticOut,
                ),
              ),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 800),
        );
}
