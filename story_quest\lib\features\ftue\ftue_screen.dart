/// First Time User Experience (FTUE) screen
/// 
/// Clean interface for account creation and login with social sign-in options
/// and privacy consent. Responsive design with accessibility support.
library;

import 'package:flutter/material.dart';

/// FTUE screen for new user onboarding
class FTUEScreen extends StatefulWidget {
  /// Callback for create account action
  final VoidCallback? onCreateAccount;
  
  /// Callback for login action
  final VoidCallback? onLogin;
  
  /// Callback for Google sign-in
  final VoidCallback? onGoogleSignIn;
  
  /// Callback for Apple sign-in
  final VoidCallback? onAppleSignIn;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const FTUEScreen({
    super.key,
    this.onCreateAccount,
    this.onLogin,
    this.onGoogleSignIn,
    this.onAppleSignIn,
    this.onBack,
  });

  @override
  State<FTUEScreen> createState() => _FTUEScreenState();
}

class _FTUEScreenState extends State<FTUEScreen> {
  bool _privacyConsent = false;

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: isTablet ? _buildTabletLayout() : _buildMobileLayout(),
      ),
    );
  }

  /// Builds layout for mobile devices
  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          const SizedBox(height: 40),
          _buildLoginOptions(),
          const SizedBox(height: 32),
          _buildSocialSignIn(),
          const SizedBox(height: 40),
          _buildPrivacySection(),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  /// Builds layout for tablets with split screen
  Widget _buildTabletLayout() {
    return Row(
      children: [
        // Left side - Login options
        Expanded(
          flex: 3,
          child: Padding(
            padding: const EdgeInsets.all(40),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                _buildHeader(),
                const SizedBox(height: 40),
                _buildLoginOptions(),
                const SizedBox(height: 32),
                _buildSocialSignIn(),
                const SizedBox(height: 20),
                _buildPrivacySection(),
                ],
              ),
            ),
          ),
        ),
        
        // Right side - Reassuring image
        Expanded(
          flex: 2,
          child: Container(
            color: const Color(0xFFF5F5F5),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.family_restroom,
                    size: 120,
                    color: const Color(0xFF1976D2),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Safe & Fun\nStory Adventures',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1976D2),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the header section
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back button
        Align(
          alignment: Alignment.centerLeft,
          child: Semantics(
            button: true,
            label: 'Go back to previous screen',
            child: IconButton(
              onPressed: widget.onBack,
              icon: const Icon(
                Icons.arrow_back,
                color: Color(0xFFB0BEC5),
                size: 28,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Title
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: const BoxDecoration(
            color: Color(0xFF1976D2),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: const Text(
            'First Time Setup',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Builds the main login options
  Widget _buildLoginOptions() {
    return Column(
      children: [
        // Create Account button
        Semantics(
          button: true,
          label: 'Create a new account to get started',
          child: SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _privacyConsent ? widget.onCreateAccount : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: _privacyConsent ? 4 : 0,
                shadowColor: _privacyConsent 
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.4)
                    : Colors.transparent,
              ),
              child: const Text(
                'Create Account',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Login button
        Semantics(
          button: true,
          label: 'Login with existing account',
          child: SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _privacyConsent ? widget.onLogin : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: _privacyConsent ? 4 : 0,
                shadowColor: _privacyConsent 
                    ? const Color(0xFF4CAF50).withValues(alpha: 0.4)
                    : Colors.transparent,
              ),
              child: const Text(
                'Login',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds social sign-in options
  Widget _buildSocialSignIn() {
    return Column(
      children: [
        const Text(
          'Or sign in with',
          style: TextStyle(
            fontSize: 16,
            color: Color(0xFF757575),
          ),
        ),
        
        const SizedBox(height: 16),
        
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Google sign-in
            Semantics(
              button: true,
              label: 'Sign in with Google',
              child: GestureDetector(
                onTap: _privacyConsent ? widget.onGoogleSignIn : null,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _privacyConsent 
                        ? const Color(0xFFB0BEC5) 
                        : const Color(0xFFE0E0E0),
                    shape: BoxShape.circle,
                    boxShadow: _privacyConsent ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: const Center(
                    child: Text(
                      'G',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            
            // Apple sign-in
            Semantics(
              button: true,
              label: 'Sign in with Apple',
              child: GestureDetector(
                onTap: _privacyConsent ? widget.onAppleSignIn : null,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _privacyConsent 
                        ? const Color(0xFFB0BEC5) 
                        : const Color(0xFFE0E0E0),
                    shape: BoxShape.circle,
                    boxShadow: _privacyConsent ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: const Icon(
                    Icons.apple,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Builds privacy consent section
  Widget _buildPrivacySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Privacy consent checkbox
        Semantics(
          label: 'Privacy consent checkbox. Required to continue.',
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Checkbox(
                value: _privacyConsent,
                onChanged: (value) {
                  setState(() {
                    _privacyConsent = value ?? false;
                  });
                },
                activeColor: const Color(0xFF4CAF50),
                materialTapTargetSize: MaterialTapTargetSize.padded,
              ),
              
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _privacyConsent = !_privacyConsent;
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(top: 12),
                    child: Text(
                      'I agree to the Privacy Policy and Terms of Service. '
                      'Story Quest is committed to protecting children\'s privacy and safety.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF757575),
                        height: 1.4,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Additional privacy notice
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: const Color(0xFFE0E0E0),
              width: 1,
            ),
          ),
          child: const Text(
            '🔒 Your child\'s safety is our priority. We follow strict privacy '
            'guidelines and never share personal information.',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF757575),
              height: 1.3,
            ),
          ),
        ),
      ],
    );
  }
}
