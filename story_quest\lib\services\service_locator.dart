/// Service locator for dependency injection
/// 
/// Configures and manages service dependencies using get_it.
/// Provides centralized access to services throughout the app.
library;

import 'package:get_it/get_it.dart';
import 'tts_service.dart';
import 'flutter_tts_service.dart';
import 'firebase_service.dart';
import 'database_service.dart';

/// Global service locator instance
final GetIt serviceLocator = GetIt.instance;

/// Initializes all services and registers them with the service locator
Future<void> setupServiceLocator() async {
  // Register TTS service
  serviceLocator.registerLazySingleton<TTSService>(
    () => FlutterTTSService(),
  );

  // Register Firebase service
  serviceLocator.registerLazySingleton<FirebaseService>(
    () => FirebaseService(),
  );

  // Register Database service
  serviceLocator.registerLazySingleton<DatabaseService>(
    () => DatabaseService(),
  );

  print('Service locator setup completed');
}

/// Disposes of all services and cleans up resources
Future<void> disposeServices() async {
  try {
    // Dispose TTS service
    final ttsService = serviceLocator.get<TTSService>();
    await ttsService.dispose();

    // Close database
    final databaseService = serviceLocator.get<DatabaseService>();
    await databaseService.close();

    // Reset service locator
    await serviceLocator.reset();
    
    print('Services disposed successfully');
  } catch (e) {
    print('Error disposing services: $e');
  }
}

/// Extension methods for easy service access
extension ServiceLocatorExtensions on GetIt {
  /// Gets the TTS service
  TTSService get tts => get<TTSService>();

  /// Gets the Firebase service
  FirebaseService get firebase => get<FirebaseService>();

  /// Gets the Database service
  DatabaseService get database => get<DatabaseService>();
}
