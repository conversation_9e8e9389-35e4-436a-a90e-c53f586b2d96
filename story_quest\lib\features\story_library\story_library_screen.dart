/// Story Library screen for browsing available stories
/// 
/// Features a 3x3 grid layout with genre filters, search functionality,
/// and offline indicators. Responsive design for all device types.
library;

import 'package:flutter/material.dart';
import '../../models/story_model.dart';
import '../../utils/accessibility_helper.dart';

/// Genre filter options
enum StoryGenre {
  all('All Stories', Icons.library_books),
  adventure('Adventure', Icons.explore),
  fantasy('Fantasy', Icons.auto_awesome),
  friendship('Friendship', Icons.favorite),
  learning('Learning', Icons.school),
  bedtime('Bedtime', Icons.bedtime);

  const StoryGenre(this.label, this.icon);
  final String label;
  final IconData icon;
}

/// Story library screen widget
class StoryLibraryScreen extends StatefulWidget {
  /// List of available stories
  final List<StoryModel> stories;
  
  /// List of downloaded story IDs
  final Set<String> downloadedStories;
  
  /// Callback when a story is selected
  final Function(StoryModel)? onStorySelected;
  
  /// Callback when download is requested
  final Function(StoryModel)? onDownloadStory;
  
  /// Callback for back navigation
  final VoidCallback? onBack;

  const StoryLibraryScreen({
    super.key,
    this.stories = const [],
    this.downloadedStories = const {},
    this.onStorySelected,
    this.onDownloadStory,
    this.onBack,
  });

  @override
  State<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends State<StoryLibraryScreen>
    with TickerProviderStateMixin {
  StoryGenre _selectedGenre = StoryGenre.all;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize filter animation
    _filterAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _filterAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    ));

    _filterAnimationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final crossAxisCount = isTablet ? 4 : 3;
    
    return Scaffold(
      backgroundColor: const Color(0xFF4CAF50), // Green background
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildFilters(),
            if (_shouldShowSearch()) _buildSearchBar(),
            Expanded(
              child: _buildStoryGrid(crossAxisCount),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the header with title and back button
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Back button
          AccessibilityHelper.createAccessibleButton(
            child: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 28,
            ),
            onPressed: widget.onBack,
            semanticLabel: 'Go back to homepage',
          ),
          
          const SizedBox(width: 16),
          
          // Title
          Expanded(
            child: AccessibilityHelper.createAccessibleText(
              'Story Library',
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    offset: Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              semanticLabel: 'Story Library - Browse and select stories',
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the genre filter tabs
  Widget _buildFilters() {
    return AnimatedBuilder(
      animation: _filterAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _filterAnimation.value,
          child: Container(
            height: 60,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: StoryGenre.values.length,
              itemBuilder: (context, index) {
                final genre = StoryGenre.values[index];
                final isSelected = genre == _selectedGenre;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: AccessibilityHelper.createAccessibleButton(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? const Color(0xFF1976D2) // Blue
                            : Colors.white.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            genre.icon,
                            color: isSelected ? Colors.white : const Color(0xFF1976D2),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            genre.label,
                            style: TextStyle(
                              color: isSelected ? Colors.white : const Color(0xFF1976D2),
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onPressed: () => _selectGenre(genre),
                    semanticLabel: 'Filter by ${genre.label}',
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Builds the search bar (shown for ages 9-12)
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AccessibilityHelper.createAccessibleFormField(
        label: 'Search stories',
        hint: 'Type to search...',
        controller: _searchController,
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  /// Builds the story grid
  Widget _buildStoryGrid(int crossAxisCount) {
    final filteredStories = _getFilteredStories();
    
    if (filteredStories.isEmpty) {
      return _buildEmptyState();
    }
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.7,
        ),
        itemCount: filteredStories.length,
        itemBuilder: (context, index) {
          final story = filteredStories[index];
          final isDownloaded = widget.downloadedStories.contains(story.storyId);
          
          return _buildStoryCard(story, isDownloaded);
        },
      ),
    );
  }

  /// Builds individual story card
  Widget _buildStoryCard(StoryModel story, bool isDownloaded) {
    return AccessibilityHelper.createAccessibleButton(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFF44336), // Red border
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Cover image (70% of card)
            Expanded(
              flex: 7,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(10),
                    ),
                    child: Container(
                      width: double.infinity,
                      color: const Color(0xFFE3F2FD),
                      child: const Icon(
                        Icons.auto_stories,
                        size: 60,
                        color: Color(0xFF1976D2),
                      ),
                    ),
                  ),
                  
                  // Offline indicator
                  if (isDownloaded)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Color(0xFF4CAF50),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  
                  // Age badge
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        story.ageGroup,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Metadata (30% of card)
            Expanded(
              flex: 3,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    Text(
                      story.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Play/Download button
                    SizedBox(
                      width: double.infinity,
                      height: 32,
                      child: ElevatedButton(
                        onPressed: () => _handleStoryAction(story, isDownloaded),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4CAF50),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 2,
                        ),
                        child: Text(
                          isDownloaded ? 'Play' : 'Download',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      onPressed: () => widget.onStorySelected?.call(story),
      semanticLabel: '${story.title}, Age ${story.ageGroup}, ${isDownloaded ? 'Downloaded' : 'Available for download'}',
    );
  }

  /// Builds empty state when no stories match filters
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.white.withValues(alpha: 0.7),
          ),
          const SizedBox(height: 16),
          AccessibilityHelper.createAccessibleText(
            'No stories found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 8),
          AccessibilityHelper.createAccessibleText(
            'Try adjusting your filters or search terms',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Selects a genre filter
  void _selectGenre(StoryGenre genre) {
    setState(() {
      _selectedGenre = genre;
    });
    
    AccessibilityHelper.announceToScreenReader('Filtered by ${genre.label}');
  }

  /// Handles story action (play or download)
  void _handleStoryAction(StoryModel story, bool isDownloaded) {
    if (isDownloaded) {
      widget.onStorySelected?.call(story);
    } else {
      widget.onDownloadStory?.call(story);
    }
  }

  /// Determines if search should be shown based on age group
  bool _shouldShowSearch() {
    // Show search for older children (9-12 age group)
    return true; // For now, always show search
  }

  /// Gets filtered stories based on genre and search
  List<StoryModel> _getFilteredStories() {
    var filtered = widget.stories;
    
    // Filter by genre
    if (_selectedGenre != StoryGenre.all) {
      // This would filter by actual genre field in the story model
      // For now, we'll show all stories
    }
    
    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((story) {
        return story.title.toLowerCase().contains(_searchQuery) ||
               story.moral.toLowerCase().contains(_searchQuery);
      }).toList();
    }
    
    return filtered;
  }
}
