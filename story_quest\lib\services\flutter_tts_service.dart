/// Flutter TTS service implementation
/// 
/// Concrete implementation of TTSService using the flutter_tts package.
/// Provides text-to-speech functionality with voice configuration support.
library;

import 'package:flutter_tts/flutter_tts.dart';
import '../models/voice_model.dart';
import 'tts_service.dart';

/// Flutter TTS service implementation
class FlutterTTSService implements TTSService {
  final FlutterTts _flutterTts = FlutterTts();
  VoiceModel? _currentVoice;
  String _currentLanguage = 'en-US';

  /// Creates a new FlutterTTSService instance
  FlutterTTSService() {
    _initializeTTS();
  }

  /// Initializes the TTS engine with default settings
  Future<void> _initializeTTS() async {
    try {
      await _flutterTts.setLanguage(_currentLanguage);
      await _flutterTts.setSpeechRate(1.0);
      await _flutterTts.setPitch(1.0);
      await _flutterTts.setVolume(1.0);
      
      // Set up completion handlers
      _flutterTts.setCompletionHandler(() {
        // Speech completed
      });
      
      _flutterTts.setErrorHandler((message) {
        // Handle TTS errors
        print('TTS Error: $message');
      });
    } catch (e) {
      print('Failed to initialize TTS: $e');
    }
  }

  @override
  Future<void> speak(String text, {VoiceModel? voiceConfig}) async {
    try {
      // Apply voice configuration if provided
      if (voiceConfig != null) {
        await _applyVoiceConfig(voiceConfig);
      }
      
      // Speak the text
      await _flutterTts.speak(text);
    } catch (e) {
      print('Failed to speak text: $e');
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    try {
      await _flutterTts.stop();
    } catch (e) {
      print('Failed to stop TTS: $e');
      rethrow;
    }
  }

  @override
  Future<void> setLanguage(String language) async {
    try {
      await _flutterTts.setLanguage(language);
      _currentLanguage = language;
    } catch (e) {
      print('Failed to set language: $e');
      rethrow;
    }
  }

  @override
  Future<void> setVoice(VoiceModel voice) async {
    try {
      await _applyVoiceConfig(voice);
      _currentVoice = voice;
    } catch (e) {
      print('Failed to set voice: $e');
      rethrow;
    }
  }

  /// Applies voice configuration to the TTS engine
  Future<void> _applyVoiceConfig(VoiceModel voice) async {
    if (!voice.isValid()) {
      throw ArgumentError('Invalid voice configuration');
    }

    try {
      // Set voice by name if available
      final voices = await getAvailableVoices();
      if (voices.contains(voice.name)) {
        await _flutterTts.setVoice({
          'name': voice.name,
          'locale': _currentLanguage,
        });
      }

      // Apply voice parameters
      await _flutterTts.setSpeechRate(voice.rate);
      await _flutterTts.setPitch(voice.pitch);
      await _flutterTts.setVolume(voice.volume);
    } catch (e) {
      print('Failed to apply voice configuration: $e');
      rethrow;
    }
  }

  @override
  Future<bool> get isSpeaking async {
    try {
      return await _flutterTts.isSpeaking;
    } catch (e) {
      print('Failed to get speaking state: $e');
      return false;
    }
  }

  @override
  Future<List<String>> getAvailableVoices() async {
    try {
      final voices = await _flutterTts.getVoices;
      if (voices is List) {
        return voices
            .where((voice) => voice is Map && voice['name'] != null)
            .map((voice) => voice['name'] as String)
            .toList();
      }
      return [];
    } catch (e) {
      print('Failed to get available voices: $e');
      return [];
    }
  }

  @override
  Future<List<String>> getAvailableLanguages() async {
    try {
      final languages = await _flutterTts.getLanguages;
      if (languages is List) {
        return languages.cast<String>();
      }
      return [];
    } catch (e) {
      print('Failed to get available languages: $e');
      return [];
    }
  }

  @override
  Future<void> setSpeechRate(double rate) async {
    if (rate < 0.1 || rate > 3.0) {
      throw ArgumentError('Speech rate must be between 0.1 and 3.0');
    }
    
    try {
      await _flutterTts.setSpeechRate(rate);
      if (_currentVoice != null) {
        _currentVoice = _currentVoice!.copyWith(rate: rate);
      }
    } catch (e) {
      print('Failed to set speech rate: $e');
      rethrow;
    }
  }

  @override
  Future<void> setPitch(double pitch) async {
    if (pitch < 0.5 || pitch > 2.0) {
      throw ArgumentError('Pitch must be between 0.5 and 2.0');
    }
    
    try {
      await _flutterTts.setPitch(pitch);
      if (_currentVoice != null) {
        _currentVoice = _currentVoice!.copyWith(pitch: pitch);
      }
    } catch (e) {
      print('Failed to set pitch: $e');
      rethrow;
    }
  }

  @override
  Future<void> setVolume(double volume) async {
    if (volume < 0.0 || volume > 1.0) {
      throw ArgumentError('Volume must be between 0.0 and 1.0');
    }
    
    try {
      await _flutterTts.setVolume(volume);
      if (_currentVoice != null) {
        _currentVoice = _currentVoice!.copyWith(volume: volume);
      }
    } catch (e) {
      print('Failed to set volume: $e');
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    try {
      await _flutterTts.pause();
    } catch (e) {
      print('Failed to pause TTS: $e');
      rethrow;
    }
  }

  @override
  Future<void> resume() async {
    try {
      // Note: flutter_tts doesn't have a direct resume method
      // This is a placeholder for future implementation
      print('Resume not directly supported by flutter_tts');
    } catch (e) {
      print('Failed to resume TTS: $e');
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      await stop();
      // Additional cleanup if needed
    } catch (e) {
      print('Failed to dispose TTS service: $e');
    }
  }

  /// Gets the current voice configuration
  VoiceModel? get currentVoice => _currentVoice;

  /// Gets the current language
  String get currentLanguage => _currentLanguage;
}
